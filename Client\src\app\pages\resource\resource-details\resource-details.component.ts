import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';
import { ResourcesService } from '../../../Core/Services/resources.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  Resource,
  ApiResponse,
  ResourceType,
} from '../../../Core/Models/resources';
import { MessageService } from 'primeng/api';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-resource-details',
  standalone: false,
  templateUrl: './resource-details.component.html',
  styleUrls: ['./resource-details.component.scss'],
  providers: [MessageService],
})
export class ResourceDetailsComponent implements OnInit {
  resource: Resource | null = null;
  isLoading = false;
  error: string | null = null;
  resourceTypes = ResourceType;
  private apiUrl = environment.apiUrl;

  // Utility method to get full image path
  getFullImagePath(relativePath: string): string {
    if (!relativePath) return 'assets/images/placeholder.jpg';

    // Check if it's already a full URL
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Handle paths for ResourceImages and ResourceLogos folders
    if (
      relativePath.includes('ResourceImages') ||
      relativePath.includes('ResourceLogos')
    ) {
      // Fix double slash issue and use the Files controller
      return `${this.apiUrl}/api/Files${relativePath.replace(/^\/+/, '/')}`;
    }

    // Default fallback
    return `${this.apiUrl}${relativePath.startsWith('/') ? '' : '/'}${relativePath}`;
  }

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resourceService: ResourcesService,
    private messageService: MessageService,
  ) {}

  ngOnInit(): void {
    this.loadResourceDetails();
  }

  // Helper method to safely access nested properties
  getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((prev, curr) => {
      return prev ? prev[curr] : null;
    }, obj);
  }

  loadResourceDetails(): void {
    this.isLoading = true;
    this.error = null;

    const id = this.route.snapshot.paramMap.get('id');

    if (id) {
      const numericId = Number(id);

      this.resourceService.GetResourceById(numericId).subscribe({
        next: (response: ApiResponse<Resource>) => {
          // Check if the response itself is the resource (no data property)
          if (response && !response.data) {
            this.resource = response as any;
          } else {
            this.resource = response.data || null;
          }

          this.isLoading = false;

          if (!this.resource) {
            this.error = 'Resource not found';
            console.error('Resource data is null or undefined');
          }
        },
        error: (error: HttpErrorResponse) => {
          console.error('API Error:', error);
          this.error = error.message;
          this.isLoading = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.message || 'Failed to load resource details',
          });
        },
      });
    } else {
      this.error = 'Resource ID not provided';
      this.isLoading = false;
      console.error('No ID parameter found in the route');
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Resource ID not provided',
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/resources']);
  }

  editResource() {
    if (this.resource && this.resource.id) {
      // Update the navigation path to match the route defined in your routing module
      this.router.navigate(['/resources/edit-resource', this.resource.id]);
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Cannot edit resource: Resource ID is missing',
      });
    }
  }

  getResourceTypeName(type: ResourceType): string {
    switch (type) {
      case ResourceType.ExternalPartner:
        return 'External Partner';
      case ResourceType.SouthWardPromiseNeighbourhood:
        return 'South Ward Promise Neighborhood';
      case ResourceType.SWPNPartner:
        return 'SWPN Partner';
      default:
        return 'Unknown';
    }
  }
}

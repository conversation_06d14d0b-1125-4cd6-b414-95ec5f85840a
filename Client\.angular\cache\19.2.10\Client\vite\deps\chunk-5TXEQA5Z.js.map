{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-message.mjs"], "sourcesContent": ["import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, signal, inject, booleanAttribute, ContentChildren, ContentChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { TimesIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"container\"];\nconst _c1 = [\"icon\"];\nconst _c2 = [\"closeicon\"];\nconst _c3 = [\"*\"];\nconst _c4 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c5 = a0 => ({\n  value: \"visible()\",\n  params: a0\n});\nconst _c6 = a0 => ({\n  closeCallback: a0\n});\nfunction Message_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Message_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Message_Conditional_0_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate || ctx_r0.iconTemplate);\n  }\n}\nfunction Message_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.icon);\n  }\n}\nfunction Message_Conditional_0_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.cx(\"text\"))(\"innerHTML\", ctx_r0.text, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Message_Conditional_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, Message_Conditional_0_div_4_span_1_Template, 1, 2, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.escape);\n  }\n}\nfunction Message_Conditional_0_ng_template_5_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.cx(\"text\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.text);\n  }\n}\nfunction Message_Conditional_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Message_Conditional_0_ng_template_5_span_0_Template, 2, 2, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.escape && ctx_r0.text);\n  }\n}\nfunction Message_Conditional_0_Conditional_7_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Message_Conditional_0_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Message_Conditional_0_Conditional_7_ng_container_0_Template, 1, 0, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.containerTemplate || ctx_r0.containerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c6, ctx_r0.close.bind(ctx_r0)));\n  }\n}\nfunction Message_Conditional_0_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.cx(\"text\"));\n  }\n}\nfunction Message_Conditional_0_Conditional_9_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.closeIcon);\n  }\n}\nfunction Message_Conditional_0_Conditional_9_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Message_Conditional_0_Conditional_9_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Message_Conditional_0_Conditional_9_Conditional_2_ng_container_0_Template, 1, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.closeIconTemplate || ctx_r0._closeIconTemplate);\n  }\n}\nfunction Message_Conditional_0_Conditional_9_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 14);\n  }\n}\nfunction Message_Conditional_0_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function Message_Conditional_0_Conditional_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.close($event));\n    });\n    i0.ɵɵtemplate(1, Message_Conditional_0_Conditional_9_Conditional_1_Template, 1, 1, \"i\", 13)(2, Message_Conditional_0_Conditional_9_Conditional_2_Template, 1, 1, \"ng-container\")(3, Message_Conditional_0_Conditional_9_Conditional_3_Template, 1, 0, \"TimesIcon\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.closeAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.closeIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.closeIconTemplate || ctx_r0._closeIconTemplate ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r0.closeIconTemplate && !ctx_r0._closeIconTemplate && !ctx_r0.closeIcon ? 3 : -1);\n  }\n}\nfunction Message_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtemplate(2, Message_Conditional_0_Conditional_2_Template, 1, 1, \"ng-container\")(3, Message_Conditional_0_Conditional_3_Template, 1, 1, \"i\", 3)(4, Message_Conditional_0_div_4_Template, 2, 1, \"div\", 4)(5, Message_Conditional_0_ng_template_5_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(7, Message_Conditional_0_Conditional_7_Template, 1, 4, \"ng-container\")(8, Message_Conditional_0_Conditional_8_Template, 2, 1, \"span\", 5)(9, Message_Conditional_0_Conditional_9_Template, 4, 4, \"button\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const escapeOut_r3 = i0.ɵɵreference(6);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass)(\"@messageAnimation\", i0.ɵɵpureFunction1(13, _c5, i0.ɵɵpureFunction2(10, _c4, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)));\n    i0.ɵɵattribute(\"aria-live\", \"polite\")(\"role\", \"alert\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.iconTemplate || ctx_r0._iconTemplate ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.icon ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.escape)(\"ngIfElse\", escapeOut_r3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(ctx_r0.containerTemplate || ctx_r0._containerTemplate ? 7 : 8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.closable ? 9 : -1);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-message {\n    border-radius: ${dt('message.border.radius')};\n    outline-width: ${dt('message.border.width')};\n    outline-style: solid;\n}\n\n.p-message-content {\n    display: flex;\n    align-items: center;\n    padding: ${dt('message.content.padding')};\n    gap: ${dt('message.content.gap')};\n    height: 100%;\n}\n\n.p-message-icon {\n    flex-shrink: 0;\n}\n\n.p-message-close-button {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n    margin-inline-start: auto;\n    overflow: hidden;\n    position: relative;\n    width: ${dt('message.close.button.width')};\n    height: ${dt('message.close.button.height')};\n    border-radius: ${dt('message.close.button.border.radius')};\n    background: transparent;\n    transition: background ${dt('message.transition.duration')}, color ${dt('message.transition.duration')}, outline-color ${dt('message.transition.duration')}, box-shadow ${dt('message.transition.duration')}, opacity 0.3s;\n    outline-color: transparent;\n    color: inherit;\n    padding: 0;\n    border: none;\n    cursor: pointer;\n    user-select: none;\n}\n\n.p-message-close-icon {\n    font-size: ${dt('message.close.icon.size')};\n    width: ${dt('message.close.icon.size')};\n    height: ${dt('message.close.icon.size')};\n}\n\n.p-message-close-button:focus-visible {\n    outline-width: ${dt('message.close.button.focus.ring.width')};\n    outline-style: ${dt('message.close.button.focus.ring.style')};\n    outline-offset: ${dt('message.close.button.focus.ring.offset')};\n}\n\n.p-message-info {\n    background: ${dt('message.info.background')};\n    outline-color: ${dt('message.info.border.color')};\n    color: ${dt('message.info.color')};\n    box-shadow: ${dt('message.info.shadow')};\n}\n\n.p-message-info .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.info.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.info.close.button.focus.ring.shadow')};\n}\n\n.p-message-info .p-message-close-button:hover {\n    background: ${dt('message.info.close.button.hover.background')};\n}\n\n.p-message-info.p-message-outlined {\n    color: ${dt('message.info.outlined.color')};\n    outline-color: ${dt('message.info.outlined.border.color')};\n}\n\n.p-message-info.p-message-simple {\n    color: ${dt('message.info.simple.color')};\n}\n\n.p-message-success {\n    background: ${dt('message.success.background')};\n    outline-color: ${dt('message.success.border.color')};\n    color: ${dt('message.success.color')};\n    box-shadow: ${dt('message.success.shadow')};\n}\n\n.p-message-success .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.success.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.success.close.button.focus.ring.shadow')};\n}\n\n.p-message-success .p-message-close-button:hover {\n    background: ${dt('message.success.close.button.hover.background')};\n}\n\n.p-message-success.p-message-outlined {\n    color: ${dt('message.success.outlined.color')};\n    outline-color: ${dt('message.success.outlined.border.color')};\n}\n\n.p-message-success.p-message-simple {\n    color: ${dt('message.success.simple.color')};\n}\n\n.p-message-warn {\n    background: ${dt('message.warn.background')};\n    outline-color: ${dt('message.warn.border.color')};\n    color: ${dt('message.warn.color')};\n    box-shadow: ${dt('message.warn.shadow')};\n}\n\n.p-message-warn .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.warn.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.warn.close.button.focus.ring.shadow')};\n}\n\n.p-message-warn .p-message-close-button:hover {\n    background: ${dt('message.warn.close.button.hover.background')};\n}\n\n.p-message-warn.p-message-outlined {\n    color: ${dt('message.warn.outlined.color')};\n    outline-color: ${dt('message.warn.outlined.border.color')};\n}\n\n.p-message-warn.p-message-simple {\n    color: ${dt('message.warn.simple.color')};\n}\n\n.p-message-error {\n    background: ${dt('message.error.background')};\n    outline-color: ${dt('message.error.border.color')};\n    color: ${dt('message.error.color')};\n    box-shadow: ${dt('message.error.shadow')};\n}\n\n.p-message-error .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.error.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.error.close.button.focus.ring.shadow')};\n}\n\n.p-message-error .p-message-close-button:hover {\n    background: ${dt('message.error.close.button.hover.background')};\n}\n\n.p-message-error.p-message-outlined {\n    color: ${dt('message.error.outlined.color')};\n    outline-color: ${dt('message.error.outlined.border.color')};\n}\n\n.p-message-error.p-message-simple {\n    color: ${dt('message.error.simple.color')};\n}\n\n.p-message-secondary {\n    background: ${dt('message.secondary.background')};\n    outline-color: ${dt('message.secondary.border.color')};\n    color: ${dt('message.secondary.color')};\n    box-shadow: ${dt('message.secondary.shadow')};\n}\n\n.p-message-secondary .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.secondary.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.secondary.close.button.focus.ring.shadow')};\n}\n\n.p-message-secondary .p-message-close-button:hover {\n    background: ${dt('message.secondary.close.button.hover.background')};\n}\n\n.p-message-secondary.p-message-outlined {\n    color: ${dt('message.secondary.outlined.color')};\n    outline-color: ${dt('message.secondary.outlined.border.color')};\n}\n\n.p-message-secondary.p-message-simple {\n    color: ${dt('message.secondary.simple.color')};\n}\n\n.p-message-contrast {\n    background: ${dt('message.contrast.background')};\n    outline-color: ${dt('message.contrast.border.color')};\n    color: ${dt('message.contrast.color')};\n    box-shadow: ${dt('message.contrast.shadow')};\n}\n\n.p-message-contrast .p-message-close-button:focus-visible {\n    outline-color: ${dt('message.contrast.close.button.focus.ring.color')};\n    box-shadow: ${dt('message.contrast.close.button.focus.ring.shadow')};\n}\n\n.p-message-contrast .p-message-close-button:hover {\n    background: ${dt('message.contrast.close.button.hover.background')};\n}\n\n.p-message-contrast.p-message-outlined {\n    color: ${dt('message.contrast.outlined.color')};\n    outline-color: ${dt('message.contrast.outlined.border.color')};\n}\n\n.p-message-contrast.p-message-simple {\n    color: ${dt('message.contrast.simple.color')};\n}\n\n.p-message-text {\n    display: inline-flex;\n    align-items: center;\n    font-size: ${dt('message.text.font.size')};\n    font-weight: ${dt('message.text.font.weight')};\n}\n\n.p-message-icon {\n    font-size: ${dt('message.icon.size')};\n    width: ${dt('message.icon.size')};\n    height: ${dt('message.icon.size')};\n}\n\n.p-message-enter-from {\n    opacity: 0;\n}\n\n.p-message-enter-active {\n    transition: opacity 0.3s;\n}\n\n.p-message.p-message-leave-from {\n    max-height: 1000px;\n}\n\n.p-message.p-message-leave-to {\n    max-height: 0;\n    opacity: 0;\n    margin: 0;\n}\n\n.p-message-leave-active {\n    overflow: hidden;\n    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1), opacity 0.3s, margin 0.3s;\n}\n\n.p-message-leave-active .p-message-close-button {\n    opacity: 0;\n}\n\n.p-message-sm .p-message-content {\n    padding: ${dt('message.content.sm.padding')};\n}\n\n.p-message-sm .p-message-text {\n    font-size: ${dt('message.text.sm.font.size')};\n}\n\n.p-message-sm .p-message-icon {\n    font-size: ${dt('message.icon.sm.size')};\n    width: ${dt('message.icon.sm.size')};\n    height: ${dt('message.icon.sm.size')};\n}\n\n.p-message-sm .p-message-close-icon {\n    font-size: ${dt('message.close.icon.sm.size')};\n    width: ${dt('message.close.icon.sm.size')};\n    height: ${dt('message.close.icon.sm.size')};\n}\n\n.p-message-lg .p-message-content {\n    padding: ${dt('message.content.lg.padding')};\n}\n\n.p-message-lg .p-message-text {\n    font-size: ${dt('message.text.lg.font.size')};\n}\n\n.p-message-lg .p-message-icon {\n    font-size: ${dt('message.icon.lg.size')};\n    width: ${dt('message.icon.lg.size')};\n    height: ${dt('message.icon.lg.size')};\n}\n\n.p-message-lg .p-message-close-icon {\n    font-size: ${dt('message.close.icon.lg.size')};\n    width: ${dt('message.close.icon.lg.size')};\n    height: ${dt('message.close.icon.lg.size')};\n}\n\n.p-message-outlined {\n    background: transparent;\n    outline-width: ${dt('message.outlined.border.width')};\n}\n\n.p-message-simple {\n    background: transparent;\n    outline-color: transparent;\n    box-shadow: none;\n}\n\n.p-message-simple .p-message-content {\n    padding: ${dt('message.simple.content.padding')};\n}\n\n.p-message-outlined .p-message-close-button:hover,\n.p-message-simple .p-message-close-button:hover {\n    background: transparent;\n}`;\nconst classes = {\n  root: ({\n    props\n  }) => ['p-message p-component p-message-' + props.severity, {\n    'p-message-simple': props.variant === 'simple'\n  }],\n  content: 'p-message-content',\n  icon: 'p-message-icon',\n  text: 'p-message-text',\n  closeButton: 'p-message-close-button',\n  closeIcon: 'p-message-close-icon'\n};\nclass MessageStyle extends BaseStyle {\n  name = 'message';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMessageStyle_BaseFactory;\n    return function MessageStyle_Factory(__ngFactoryType__) {\n      return (ɵMessageStyle_BaseFactory || (ɵMessageStyle_BaseFactory = i0.ɵɵgetInheritedFactory(MessageStyle)))(__ngFactoryType__ || MessageStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MessageStyle,\n    factory: MessageStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MessageStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Message groups a collection of contents in tabs.\n *\n * [Live Demo](https://www.primeng.org/message/)\n *\n * @module messagestyle\n *\n */\nvar MessageClasses;\n(function (MessageClasses) {\n  /**\n   * Class name of the root element\n   */\n  MessageClasses[\"root\"] = \"p-message\";\n  /**\n   * Class name of the content element\n   */\n  MessageClasses[\"content\"] = \"p-message-content\";\n  /**\n   * Class name of the icon element\n   */\n  MessageClasses[\"icon\"] = \"p-message-icon\";\n  /**\n   * Class name of the text element\n   */\n  MessageClasses[\"text\"] = \"p-message-text\";\n  /**\n   * Class name of the close button element\n   */\n  MessageClasses[\"closeButton\"] = \"p-message-close-button\";\n  /**\n   * Class name of the close icon element\n   */\n  MessageClasses[\"closeIcon\"] = \"p-message-close-icon\";\n})(MessageClasses || (MessageClasses = {}));\n\n/**\n * Message groups a collection of contents in tabs.\n * @group Components\n */\nclass Message extends BaseComponent {\n  /**\n   * Severity level of the message.\n   * @defaultValue 'info'\n   * @group Props\n   */\n  severity = 'info';\n  /**\n   * Text content.\n   * @group Props\n   */\n  text;\n  /**\n   * Whether displaying messages would be escaped or not.\n   * @deprecated Use content projection instead '<p-message>Content</p-message>'.\n   * @group Props\n   */\n  escape = true;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether the message can be closed manually using the close icon.\n   * @group Props\n   * @defaultValue false\n   */\n  closable = false;\n  /**\n   * Icon to display in the message.\n   * @group Props\n   * @defaultValue undefined\n   */\n  icon;\n  /**\n   * Icon to display in the message close button.\n   * @group Props\n   * @defaultValue undefined\n   */\n  closeIcon;\n  /**\n   * Delay in milliseconds to close the message automatically.\n   * @defaultValue undefined\n   */\n  life;\n  /**\n   * Transition options of the show animation.\n   * @defaultValue '300ms ease-out'\n   * @group Props\n   */\n  showTransitionOptions = '300ms ease-out';\n  /**\n   * Transition options of the hide animation.\n   * @defaultValue '200ms cubic-bezier(0.86, 0, 0.07, 1)'\n   * @group Props\n   */\n  hideTransitionOptions = '200ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  size;\n  /**\n   * Specifies the input variant of the component.\n   * @group Props\n   */\n  variant;\n  /**\n   * Emits when the message is closed.\n   * @param {{ originalEvent: Event }} event - The event object containing the original event.\n   * @group Emits\n   */\n  onClose = new EventEmitter();\n  get closeAriaLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n  }\n  get containerClass() {\n    const variantClass = this.variant === 'outlined' ? 'p-message-outlined' : this.variant === 'simple' ? 'p-message-simple' : '';\n    const sizeClass = this.size === 'small' ? 'p-message-sm' : this.size === 'large' ? 'p-message-lg' : '';\n    return `p-message-${this.severity} ${variantClass} ${sizeClass}`.trim() + (this.styleClass ? ' ' + this.styleClass : '');\n  }\n  visible = signal(true);\n  _componentStyle = inject(MessageStyle);\n  /**\n   * Custom template of the message container.\n   * @group Templates\n   */\n  containerTemplate;\n  /**\n   * Custom template of the message icon.\n   * @group Templates\n   */\n  iconTemplate;\n  /**\n   * Custom template of the close icon.\n   * @group Templates\n   */\n  closeIconTemplate;\n  templates;\n  _containerTemplate;\n  _iconTemplate;\n  _closeIconTemplate;\n  ngOnInit() {\n    super.ngOnInit();\n    if (this.life) {\n      setTimeout(() => {\n        this.visible.set(false);\n      }, this.life);\n    }\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'container':\n          this._containerTemplate = item.template;\n          break;\n        case 'icon':\n          this._iconTemplate = item.template;\n          break;\n        case 'closeicon':\n          this._closeIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  /**\n   * Closes the message.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  close(event) {\n    this.visible.set(false);\n    this.onClose.emit({\n      originalEvent: event\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMessage_BaseFactory;\n    return function Message_Factory(__ngFactoryType__) {\n      return (ɵMessage_BaseFactory || (ɵMessage_BaseFactory = i0.ɵɵgetInheritedFactory(Message)))(__ngFactoryType__ || Message);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Message,\n    selectors: [[\"p-message\"]],\n    contentQueries: function Message_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.iconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.closeIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    inputs: {\n      severity: \"severity\",\n      text: \"text\",\n      escape: [2, \"escape\", \"escape\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      closable: [2, \"closable\", \"closable\", booleanAttribute],\n      icon: \"icon\",\n      closeIcon: \"closeIcon\",\n      life: \"life\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      size: \"size\",\n      variant: \"variant\"\n    },\n    outputs: {\n      onClose: \"onClose\"\n    },\n    features: [i0.ɵɵProvidersFeature([MessageStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c3,\n    decls: 1,\n    vars: 1,\n    consts: [[\"escapeOut\", \"\"], [1, \"p-message\", \"p-component\", 3, \"ngClass\"], [1, \"p-message-content\"], [1, \"p-message-icon\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\"], [\"pRipple\", \"\", \"type\", \"button\", 1, \"p-message-close-button\"], [4, \"ngTemplateOutlet\"], [3, \"ngClass\", \"innerHTML\", 4, \"ngIf\"], [3, \"ngClass\", \"innerHTML\"], [3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"pRipple\", \"\", \"type\", \"button\", 1, \"p-message-close-button\", 3, \"click\"], [1, \"p-message-close-icon\", 3, \"ngClass\"], [\"styleClass\", \"p-message-close-icon\"]],\n    template: function Message_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Message_Conditional_0_Template, 10, 15, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.visible() ? 0 : -1);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, TimesIcon, Ripple, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('messageAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'translateY(-25%)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        height: 0,\n        marginTop: 0,\n        marginBottom: 0,\n        marginLeft: 0,\n        marginRight: 0,\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Message, [{\n    type: Component,\n    args: [{\n      selector: 'p-message',\n      standalone: true,\n      imports: [CommonModule, TimesIcon, Ripple, SharedModule],\n      template: `\n        @if (visible()) {\n            <div\n                class=\"p-message p-component\"\n                [attr.aria-live]=\"'polite'\"\n                [ngClass]=\"containerClass\"\n                [attr.role]=\"'alert'\"\n                [@messageAnimation]=\"{\n                    value: 'visible()',\n                    params: {\n                        showTransitionParams: showTransitionOptions,\n                        hideTransitionParams: hideTransitionOptions\n                    }\n                }\"\n            >\n                <div class=\"p-message-content\">\n                    @if (iconTemplate || _iconTemplate) {\n                        <ng-container *ngTemplateOutlet=\"iconTemplate || iconTemplate\"></ng-container>\n                    }\n                    @if (icon) {\n                        <i class=\"p-message-icon\" [ngClass]=\"icon\"></i>\n                    }\n\n                    <div *ngIf=\"!escape; else escapeOut\">\n                        <span *ngIf=\"!escape\" [ngClass]=\"cx('text')\" [innerHTML]=\"text\"></span>\n                    </div>\n\n                    <ng-template #escapeOut>\n                        <span *ngIf=\"escape && text\" [ngClass]=\"cx('text')\">{{ text }}</span>\n                    </ng-template>\n\n                    @if (containerTemplate || _containerTemplate) {\n                        <ng-container *ngTemplateOutlet=\"containerTemplate || containerTemplate; context: { closeCallback: close.bind(this) }\"></ng-container>\n                    } @else {\n                        <span [ngClass]=\"cx('text')\">\n                            <ng-content></ng-content>\n                        </span>\n                    }\n                    @if (closable) {\n                        <button pRipple type=\"button\" class=\"p-message-close-button\" (click)=\"close($event)\" [attr.aria-label]=\"closeAriaLabel\">\n                            @if (closeIcon) {\n                                <i class=\"p-message-close-icon\" [ngClass]=\"closeIcon\"></i>\n                            }\n                            @if (closeIconTemplate || _closeIconTemplate) {\n                                <ng-container *ngTemplateOutlet=\"closeIconTemplate || _closeIconTemplate\"></ng-container>\n                            }\n                            @if (!closeIconTemplate && !_closeIconTemplate && !closeIcon) {\n                                <TimesIcon styleClass=\"p-message-close-icon\" />\n                            }\n                        </button>\n                    }\n                </div>\n            </div>\n        }\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MessageStyle],\n      animations: [trigger('messageAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'translateY(-25%)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        height: 0,\n        marginTop: 0,\n        marginBottom: 0,\n        marginLeft: 0,\n        marginRight: 0,\n        opacity: 0\n      }))])])]\n    }]\n  }], null, {\n    severity: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    closable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    icon: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    life: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    variant: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    containerTemplate: [{\n      type: ContentChild,\n      args: ['container', {\n        descendants: false\n      }]\n    }],\n    iconTemplate: [{\n      type: ContentChild,\n      args: ['icon', {\n        descendants: false\n      }]\n    }],\n    closeIconTemplate: [{\n      type: ContentChild,\n      args: ['closeicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass MessageModule {\n  static ɵfac = function MessageModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MessageModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MessageModule,\n    imports: [Message, SharedModule],\n    exports: [Message, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Message, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MessageModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Message, SharedModule],\n      exports: [Message, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Message, MessageClasses, MessageModule, MessageStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,eAAe;AACjB;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,YAAY;AAAA,EAC9E;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,CAAC;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,IAAI;AAAA,EACtC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,GAAG,MAAM,CAAC,EAAE,aAAa,OAAO,MAAS,cAAc;AAAA,EACzF;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC7E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM;AAAA,EACtC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,GAAG,MAAM,CAAC;AAC1C,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,IAAI;AAAA,EAClC;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,QAAQ,EAAE;AAAA,EACxF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,UAAU,OAAO,IAAI;AAAA,EACpD;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE;AAAA,EACxG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,qBAAqB,OAAO,iBAAiB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1K;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,GAAG,MAAM,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,EAAE;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,SAAS;AAAA,EAC3C;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACrH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,qBAAqB,OAAO,kBAAkB;AAAA,EACzF;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,qEAAqE,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC;AACD,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,4DAA4D,GAAG,GAAG,cAAc,EAAE,GAAG,4DAA4D,GAAG,GAAG,aAAa,EAAE;AACrQ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,cAAc;AAClD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,IAAI,EAAE;AAC1C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,qBAAqB,OAAO,qBAAqB,IAAI,EAAE;AAC/E,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,qBAAqB,CAAC,OAAO,sBAAsB,CAAC,OAAO,YAAY,IAAI,EAAE;AAAA,EACxG;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,cAAc,EAAE,GAAG,8CAA8C,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8CAA8C,GAAG,GAAG,cAAc,EAAE,GAAG,8CAA8C,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,UAAU,CAAC;AAClgB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,cAAc,EAAE,qBAAwB,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,OAAO,uBAAuB,OAAO,qBAAqB,CAAC,CAAC;AACzL,IAAG,YAAY,aAAa,QAAQ,EAAE,QAAQ,OAAO;AACrD,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,gBAAgB,OAAO,gBAAgB,IAAI,EAAE;AACrE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,OAAO,IAAI,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM,EAAE,YAAY,YAAY;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,qBAAqB,OAAO,qBAAqB,IAAI,CAAC;AAC9E,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,WAAW,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA,qBAEe,GAAG,uBAAuB,CAAC;AAAA,qBAC3B,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAOhC,GAAG,yBAAyB,CAAC;AAAA,WACjC,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAgBvB,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,6BAA6B,CAAC;AAAA,qBAC1B,GAAG,oCAAoC,CAAC;AAAA;AAAA,6BAEhC,GAAG,6BAA6B,CAAC,WAAW,GAAG,6BAA6B,CAAC,mBAAmB,GAAG,6BAA6B,CAAC,gBAAgB,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAU9L,GAAG,yBAAyB,CAAC;AAAA,aACjC,GAAG,yBAAyB,CAAC;AAAA,cAC5B,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAItB,GAAG,uCAAuC,CAAC;AAAA,qBAC3C,GAAG,uCAAuC,CAAC;AAAA,sBAC1C,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhD,GAAG,yBAAyB,CAAC;AAAA,qBAC1B,GAAG,2BAA2B,CAAC;AAAA,aACvC,GAAG,oBAAoB,CAAC;AAAA,kBACnB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAItB,GAAG,4CAA4C,CAAC;AAAA,kBACnD,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjD,GAAG,4CAA4C,CAAC;AAAA;AAAA;AAAA;AAAA,aAIrD,GAAG,6BAA6B,CAAC;AAAA,qBACzB,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIhD,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI1B,GAAG,4BAA4B,CAAC;AAAA,qBAC7B,GAAG,8BAA8B,CAAC;AAAA,aAC1C,GAAG,uBAAuB,CAAC;AAAA,kBACtB,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIzB,GAAG,+CAA+C,CAAC;AAAA,kBACtD,GAAG,gDAAgD,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIpD,GAAG,+CAA+C,CAAC;AAAA;AAAA;AAAA;AAAA,aAIxD,GAAG,gCAAgC,CAAC;AAAA,qBAC5B,GAAG,uCAAuC,CAAC;AAAA;AAAA;AAAA;AAAA,aAInD,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI7B,GAAG,yBAAyB,CAAC;AAAA,qBAC1B,GAAG,2BAA2B,CAAC;AAAA,aACvC,GAAG,oBAAoB,CAAC;AAAA,kBACnB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAItB,GAAG,4CAA4C,CAAC;AAAA,kBACnD,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjD,GAAG,4CAA4C,CAAC;AAAA;AAAA;AAAA;AAAA,aAIrD,GAAG,6BAA6B,CAAC;AAAA,qBACzB,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIhD,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI1B,GAAG,0BAA0B,CAAC;AAAA,qBAC3B,GAAG,4BAA4B,CAAC;AAAA,aACxC,GAAG,qBAAqB,CAAC;AAAA,kBACpB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIvB,GAAG,6CAA6C,CAAC;AAAA,kBACpD,GAAG,8CAA8C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIlD,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA,aAItD,GAAG,8BAA8B,CAAC;AAAA,qBAC1B,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIjD,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI3B,GAAG,8BAA8B,CAAC;AAAA,qBAC/B,GAAG,gCAAgC,CAAC;AAAA,aAC5C,GAAG,yBAAyB,CAAC;AAAA,kBACxB,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,qBAI3B,GAAG,iDAAiD,CAAC;AAAA,kBACxD,GAAG,kDAAkD,CAAC;AAAA;AAAA;AAAA;AAAA,kBAItD,GAAG,iDAAiD,CAAC;AAAA;AAAA;AAAA;AAAA,aAI1D,GAAG,kCAAkC,CAAC;AAAA,qBAC9B,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIrD,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI/B,GAAG,6BAA6B,CAAC;AAAA,qBAC9B,GAAG,+BAA+B,CAAC;AAAA,aAC3C,GAAG,wBAAwB,CAAC;AAAA,kBACvB,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAI1B,GAAG,gDAAgD,CAAC;AAAA,kBACvD,GAAG,iDAAiD,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIrD,GAAG,gDAAgD,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzD,GAAG,iCAAiC,CAAC;AAAA,qBAC7B,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIpD,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAM/B,GAAG,wBAAwB,CAAC;AAAA,mBAC1B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIhC,GAAG,mBAAmB,CAAC;AAAA,aAC3B,GAAG,mBAAmB,CAAC;AAAA,cACtB,GAAG,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eA+BtB,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAI9B,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAI/B,GAAG,sBAAsB,CAAC;AAAA,aAC9B,GAAG,sBAAsB,CAAC;AAAA,cACzB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIvB,GAAG,4BAA4B,CAAC;AAAA,aACpC,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,eAI/B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAI9B,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAI/B,GAAG,sBAAsB,CAAC;AAAA,aAC9B,GAAG,sBAAsB,CAAC;AAAA,cACzB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIvB,GAAG,4BAA4B,CAAC;AAAA,aACpC,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKzB,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAUzC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOnD,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,qCAAqC,MAAM,UAAU;AAAA,IAC1D,oBAAoB,MAAM,YAAY;AAAA,EACxC,CAAC;AAAA,EACD,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AACb;AACA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,iBAAgB;AAIzB,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,SAAS,IAAI;AAI5B,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,aAAa,IAAI;AAIhC,EAAAA,gBAAe,WAAW,IAAI;AAChC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAM1C,IAAM,UAAN,MAAM,iBAAgB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,IAAI,aAAa;AAAA,EAC3B,IAAI,iBAAiB;AACnB,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,EAC7E;AAAA,EACA,IAAI,iBAAiB;AACnB,UAAM,eAAe,KAAK,YAAY,aAAa,uBAAuB,KAAK,YAAY,WAAW,qBAAqB;AAC3H,UAAM,YAAY,KAAK,SAAS,UAAU,iBAAiB,KAAK,SAAS,UAAU,iBAAiB;AACpG,WAAO,aAAa,KAAK,QAAQ,IAAI,YAAY,IAAI,SAAS,GAAG,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,aAAa;AAAA,EACvH;AAAA,EACA,UAAU,OAAO,IAAI;AAAA,EACrB,kBAAkB,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,QAAI,KAAK,MAAM;AACb,iBAAW,MAAM;AACf,aAAK,QAAQ,IAAI,KAAK;AAAA,MACxB,GAAG,KAAK,IAAI;AAAA,IACd;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,qBAAqB,KAAK;AAC/B;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,qBAAqB,KAAK;AAC/B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO;AACX,SAAK,QAAQ,IAAI,KAAK;AACtB,SAAK,QAAQ,KAAK;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,MAAM;AAAA,MACN,WAAW;AAAA,MACX,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,YAAY,CAAC,GAAM,0BAA0B;AAAA,IAC/E,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,aAAa,eAAe,GAAG,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,WAAW,IAAI,QAAQ,UAAU,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,WAAW,aAAa,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,WAAW,IAAI,QAAQ,UAAU,GAAG,0BAA0B,GAAG,OAAO,GAAG,CAAC,GAAG,wBAAwB,GAAG,SAAS,GAAG,CAAC,cAAc,sBAAsB,CAAC;AAAA,IAC7jB,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,gCAAgC,IAAI,IAAI,OAAO,CAAC;AAAA,MACnE;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,QAAQ,IAAI,IAAI,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAkB,WAAW,QAAQ,YAAY;AAAA,IACtG,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACnE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QACzG,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,QAAQ,YAAY;AAAA,MACvD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuDV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,YAAY;AAAA,MACxB,YAAY,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACpE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QACzG,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS,YAAY;AAAA,IAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,EACjC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,SAAS,cAAc,YAAY;AAAA,EAC/C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS,YAAY;AAAA,MAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["MessageClasses"]}
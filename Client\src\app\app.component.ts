import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { AuthService } from './Core/Services/auth.service';
import { NotificationService } from './Core/Services/notification.service';
import { TokenExpiryService } from './Core/Services/token-expiry.service';
import { UserAvatarService } from './Core/Services/user-avatar.service';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  isLoggedIn: boolean = false;
  private routerSubscription: Subscription | null = null;

  constructor(
    private readonly authservice: AuthService,
    private readonly router: Router,
    private readonly notificationService: NotificationService,
    private readonly tokenExpiryService: TokenExpiryService,
    private readonly userAvatarService: UserAvatarService,
  ) {
    // Only initialize notification service once after login
    // We'll do this in ngOnInit instead of on every navigation
  }

  ngOnInit() {
    if (this.authservice.isLoggedIn()) {
      this.authservice.refreshUserInfoFromToken();

      // Initialize notification service once if user is logged in and not on auth routes
      if (!this.isAuthRoute()) {
        this.notificationService.initialize();
      }

      // Start token expiry monitoring
      this.tokenExpiryService.startMonitoring();

      // Initialize user avatar if logged in
      this.userAvatarService.setRandomAvatar();
    }

    this.isLoggedIn = this.authservice.isLoggedIn();

    // Listen for route changes to update login status
    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        const wasLoggedIn = this.isLoggedIn;
        this.isLoggedIn = this.authservice.isLoggedIn();

        // If user just logged in, start token monitoring
        if (!wasLoggedIn && this.isLoggedIn) {
          this.tokenExpiryService.startMonitoring();
          // Set a random avatar when user logs in
          this.userAvatarService.setRandomAvatar();
        }
        // If user just logged out, stop token monitoring
        else if (wasLoggedIn && !this.isLoggedIn) {
          this.tokenExpiryService.stopMonitoring();
        }
      });
  }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: MouseEvent) {
    const headerComponent = document.querySelector('app-header');
  }

  isAuthRoute(): boolean {
    const currentUrl = this.router.url;
    return (
      currentUrl.includes('/auth/login') ||
      currentUrl.includes('/auth/VerifyOtp')
    );
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    // Stop token monitoring
    this.tokenExpiryService.stopMonitoring();
  }
}

<div class="otp-container">
  <div class="otp-card">
    <div class="otp-header">
      <h1>OTP Verification</h1>
      <p>Please enter the verification code sent to your email</p>
      <p class="otp-validity-note">Your OTP is valid for 5 minutes</p>

      <!-- Timer for resend OTP button -->
      <div class="timer-container">
        <p-progressBar
          [value]="(remainingTime / 30) * 100"
          [showValue]="false"
          [style]="{ height: '6px' }"
          [class.time-warning]="remainingTime < 10"
        ></p-progressBar>
        <div class="timer-text" [class.time-warning]="remainingTime < 10">
          <i class="pi pi-clock"></i>
          <span *ngIf="remainingTime > 0"
            >{{ formattedTime }} until resend available</span
          >
          <span *ngIf="remainingTime <= 0">Resend OTP available</span>
        </div>
      </div>
    </div>

    <form [formGroup]="otpForm" (ngSubmit)="onVerify()">
      <!-- Error message -->
      <div *ngIf="errorMessage" class="error-message">
        <p-message severity="error" [text]="errorMessage"></p-message>
      </div>

      <!-- OTP Input -->
      <div class="otp-input-container">
        <p-inputotp
          formControlName="otp"
          [length]="5"
          [mask]="true"
          [autoFocus]="true"
          styleClass="otp-input"
        ></p-inputotp>

        <small
          *ngIf="otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched"
          class="p-error block"
        >
          <span *ngIf="otpForm.get('otp')?.errors?.['required']"
            >OTP is required</span
          >
          <span
            *ngIf="
              otpForm.get('otp')?.errors?.['minlength'] ||
              otpForm.get('otp')?.errors?.['maxlength']
            "
          >
            OTP must be 5 digits
          </span>
          <span *ngIf="otpForm.get('otp')?.errors?.['pattern']"
            >OTP must contain only numbers</span
          >
        </small>
      </div>

      <div class="form-actions-wrapper">
        <div class="form-actions">
          <div class="buttons-container">
            <p-button
              type="submit"
              styleClass="verify-button"
              [disabled]="otpForm.invalid || isLoading"
            >
              <ng-container *ngIf="isLoading; else verifyText">
                <p-progressSpinner
                  [style]="{ width: '24px', height: '24px' }"
                  strokeWidth="4"
                  animationDuration=".5s"
                ></p-progressSpinner>
              </ng-container>
              <ng-template #verifyText>
                <span>Verify</span>
              </ng-template>
            </p-button>

            <div class="resend-container">
              <p-button
                styleClass="p-button-text p-button-sm resend-button"
                (onClick)="resendOtp()"
                icon="pi pi-refresh"
                label="Resend OTP"
                [disabled]="isLoading || remainingTime > 0"
              ></p-button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

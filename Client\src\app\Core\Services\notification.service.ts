import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { EventsService } from './events.service';
import { Event } from '../Models/events';
import { EventPaginationParams } from '../Models/pagination.events.interface';
import { AuthService } from './auth.service';
import { Router } from '@angular/router';
import { DateUtilsService } from './date-utils.service';

export interface Notification {
  id: number;
  title: string;
  message: string;
  timestamp: Date;
  status:
    | 'Draft'
    | 'Pending Review'
    | 'Approved'
    | 'Rejected'
    | 'Event Started';
  eventId: number;
  submitterName: string;
  organizerName?: string;
  timeAgo: string;
  startDate: string;
  endDate: string;
  startTime: string | undefined;
  endTime: string | undefined;
  displayStartTime: boolean;
  displayEndTime: boolean;
  eventStarts?: Date; // Added to check if event has started
}

export interface NotificationGroup {
  title: string;
  notifications: Notification[];
}

@Injectable({
  providedIn: 'root',
})
export class NotificationService implements OnDestroy {
  private notifications = new BehaviorSubject<Notification[]>([]);
  private isLoading = new BehaviorSubject<boolean>(false);
  private unreadCount = new BehaviorSubject<number>(0);
  private refreshInterval: any;
  private readonly REFRESH_INTERVAL = 30000; // 30 seconds
  private isInitialized = false;

  constructor(
    private eventsService: EventsService,
    private authService: AuthService,
    private router: Router,
    private dateUtils: DateUtilsService,
  ) {}

  initialize(): void {
    if (this.isInitialized || !this.authService.isLoggedIn()) {
      return;
    }

    // Check if we're on the login or OTP page - don't initialize in that case
    const currentUrl = this.router.url;
    if (
      currentUrl.includes('/auth/login') ||
      currentUrl.includes('/auth/VerifyOtp')
    ) {
      return;
    }

    this.isInitialized = true;
    this.startAutoRefresh();
  }

  private startAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    if (
      !this.router.url.includes('/auth/login') &&
      !this.router.url.includes('/auth/VerifyOtp')
    ) {
      this.refreshInterval = setInterval(() => {}, this.REFRESH_INTERVAL);
    }
  }

  getNotifications(): Observable<Notification[]> {
    return this.notifications.asObservable();
  }

  getNotificationGroups(): Observable<NotificationGroup[]> {
    return this.notifications
      .asObservable()
      .pipe(
        map((notifications) => this.groupNotificationsByDate(notifications)),
      );
  }

  getIsLoading(): Observable<boolean> {
    return this.isLoading.asObservable();
  }

  getUnreadCount(): Observable<number> {
    return this.unreadCount.asObservable();
  }

  loadNotifications(): void {
    // Skip loading if not authenticated
    if (!this.authService.isLoggedIn()) {
      this.notifications.next([]);
      this.unreadCount.next(0);
      this.isLoading.next(false);
      return;
    }

    // Skip loading if we're on the event list page to avoid duplicate API calls
    if (this.router.url.includes('/events/event-list')) {
      return;
    }

    this.isLoading.next(true);

    const params: EventPaginationParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: 'submittedOn',
      sortOrder: 'desc',
      filters: {
        eventStatus: 'Draft,Pending Review,Approved,Rejected',
      },
    };

    this.eventsService.getEvents(params).subscribe({
      next: (response) => {
        if (response && response.items) {
          const notifications = this.mapEventsToNotifications(response.items);

          this.notifications.next(notifications);
          this.unreadCount.next(notifications.length);
          this.isLoading.next(false);
        } else {
          this.notifications.next([]);
          this.unreadCount.next(0);
          this.isLoading.next(false);
        }
      },
      error: (error) => {
        this.notifications.next([]);
        this.unreadCount.next(0);
        this.isLoading.next(false);
      },
    });
  }

  markAllAsRead(): void {
    this.unreadCount.next(0);
  }

  ngOnDestroy(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  }

  private mapEventsToNotifications(events: Event[]): Notification[] {
    return events.map((event) => {
      // Initialize with a default value to avoid "used before assigned" error
      let status:
        | 'Draft'
        | 'Pending Review'
        | 'Approved'
        | 'Rejected'
        | 'Event Started' = 'Draft';

      // Check if the event has started
      const hasEventStarted = this.hasEventStarted(event);

      switch (event.statusName) {
        case 'Draft':
          status = 'Draft';
          break;
        case 'Submitted':
        case 'Pending Review':
          status = 'Pending Review';
          break;
        case 'Approved':
          // If event is approved and has started, show "Event Has Started" status
          status = hasEventStarted ? 'Event Started' : 'Approved';
          break;
        case 'Rejected':
          status = 'Rejected';
          break;
      }

      // Format dates in the format "14 Sep 2023, 5:30 PM" - This is for display purposes only
      // Convert UTC dates to IST before displaying
      const startDate = this.dateUtils.formatDateForDisplay(event.eventStarts);
      const endDate = this.dateUtils.formatDateForDisplay(event.eventEnds);

      // Use createdAt for both timestamp and timeAgo to ensure consistency
      const timestamp = event.createdAt || new Date();

      return {
        id: event.id,
        title: event.title,
        message: '',
        timestamp: timestamp,
        status: status,
        eventId: event.id,
        submitterName: event.submitterName || 'Unknown',
        organizerName: event.organizerName || 'Unknown',
        timeAgo: this.dateUtils.getTimeAgo(timestamp),
        startDate: startDate,
        endDate: endDate,
        startTime: event.startTime,
        endTime: event.endTime,
        displayStartTime: event.displayStartTime,
        displayEndTime: event.displayEndTime,
        eventStarts: event.eventStarts,
      };
    });
  }

  // Check if the event has already started
  private hasEventStarted(event: Event): boolean {
    if (!event || !event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    return now >= eventStartTime;
  }

  private groupNotificationsByDate(
    notifications: Notification[],
  ): NotificationGroup[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const groups: NotificationGroup[] = [];

    // Today's notifications
    const todayNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() === today.getTime();
    });

    if (todayNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      todayNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Today',
        notifications: todayNotifications,
      });
    }

    // Yesterday's notifications
    const yesterdayNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() === yesterday.getTime();
    });

    if (yesterdayNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      yesterdayNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Yesterday',
        notifications: yesterdayNotifications,
      });
    }

    // Older notifications
    const olderNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() < yesterday.getTime();
    });

    if (olderNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      olderNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Older',
        notifications: olderNotifications,
      });
    }

    return groups;
  }
}

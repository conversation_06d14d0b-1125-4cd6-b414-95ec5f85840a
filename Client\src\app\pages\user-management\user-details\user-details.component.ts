import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserDetailsService } from '../../../Core/Services/UserDetails.service';
import { User } from '../../../Core/Models/User';
import { MessageService } from 'primeng/api';
import { AuthService } from '../../../Core/Services/auth.service';

@Component({
  selector: 'app-user-details',
  standalone: false,
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss'],
  providers: [MessageService],
})
export class UserDetailsComponent implements OnInit {
  user: User | null = null;
  isLoading = false;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserDetailsService,
    private messageService: MessageService,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.userService.getUserById(id).subscribe({
        next: (user) => {
          this.user = user;
          this.isLoading = false;
        },
        error: (error) => {
          this.error = error.message;
          this.isLoading = false;
        },
      });
    } else {
      this.error = 'User ID not provided';
      this.isLoading = false;
    }
  }

  goBack(): void {
    this.router.navigate(['/user-management']);
  }

  editUser(): void {
    if (this.user && this.user.id) {
      this.router.navigate(['/user-management/edit-user', this.user.id]);
    }
  }

  toggleUserStatus(): void {
    if (this.user && this.user.id) {
      const newStatus = !this.user.isActive;

      // Check if user is trying to deactivate themselves
      const currentUser = this.authService.getUserInfo();
      if (!newStatus && currentUser && currentUser.id === this.user.id) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'You cannot deactivate your own account.',
        });
        return;
      }

      this.isLoading = true;

      this.userService.toggleUserStatus(this.user.id, newStatus).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            if (this.user) {
              this.user.isActive = newStatus;
            }
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: `User ${newStatus ? 'activated' : 'deactivated'} successfully`,
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail:
                response.message ||
                `Failed to ${newStatus ? 'activate' : 'deactivate'} user`,
            });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.error = error.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to ${newStatus ? 'activate' : 'deactivate'} user`,
          });
          this.isLoading = false;
        },
      });
    }
  }
}

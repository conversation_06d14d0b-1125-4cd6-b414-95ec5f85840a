<p-toast position="top-right"></p-toast>
<p-confirmDialog
  key="toggleUserStatus"
  styleClass="status-confirmation"
  [style]="{ width: '450px' }"
  [baseZIndex]="10000"
  [autoZIndex]="true"
>
</p-confirmDialog>

<div class="container mt-4 px-3 pb-3">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>User Management</h2>

    <div class="d-flex gap-3">
      <button
        pButton
        class="p-button-outlined p-button-danger me-2"
        (click)="toggleFilters()"
      >
        <i class="pi pi-filter me-1"></i>
        {{ showFilters ? "Hide" : "Filters" }}
      </button>

      <button *ngIf="isGlobalAdmin" class="btn btn-danger" (click)="addUser()">
        <i class="bi bi-plus-lg me-1"></i>
        Add User
      </button>
    </div>
  </div>

  <!-- Filter Panel -->
  <div *ngIf="showFilters" class="mb-3 border rounded p-3 bg-light">
    <form [formGroup]="filterForm">
      <div class="row">
        <!-- Name -->
        <div class="col-md-3 mb-3">
          <label class="form-label">Name</label>
          <span class="p-input-icon-left w-100">
            <i class="pi pi-search"></i>
            <input
              type="text"
              pInputText
              class="w-100"
              formControlName="name"
              placeholder="Filter by name"
            />
          </span>
        </div>

        <!-- Created By -->
        <div class="col-md-3 mb-3">
          <label class="form-label">Created By</label>
          <span class="p-input-icon-left w-100">
            <i class="pi pi-search"></i>
            <input
              type="text"
              pInputText
              class="w-100"
              formControlName="createdByName"
              placeholder="Filter by creator"
            />
          </span>
        </div>

        <!-- Role -->
        <div class="col-md-3 mb-3">
          <label class="form-label">Role</label>
          <p-dropdown
            [options]="roleOptions"
            formControlName="role"
            optionLabel="name"
            optionValue="value"
            placeholder="All Roles"
            [showClear]="true"
            styleClass="w-100"
          ></p-dropdown>
        </div>

        <!-- Status -->
        <div class="col-md-3 mb-3">
          <label class="form-label">Status</label>
          <p-dropdown
            [options]="statusOptions"
            formControlName="status"
            optionLabel="name"
            optionValue="value"
            placeholder="All Status"
            [showClear]="true"
            styleClass="w-100"
          ></p-dropdown>
        </div>
      </div>

      <!-- Clear Filters Button -->
      <div class="d-flex justify-content-end">
        <p-button
          label="Clear Filters"
          icon="pi pi-filter-slash"
          styleClass="p-button-outlined p-button-secondary"
          (onClick)="clearFilters()"
        ></p-button>
      </div>
    </form>
  </div>

  <div class="table-container border rounded-3 overflow-hidden bg-white">
    <div class="table-responsive">
      <table class="table custom-row-lines table-hover mb-0">
        <thead class="table-head text-white sticky-top">
          <tr>
            <th
              class="sortable-header"
              (click)="sortUsers('fullName')"
              style="cursor: pointer"
            >
              Name
              <i class="bi" [ngClass]="getSortIcon('fullName')"></i>
            </th>
            <th
              class="sortable-header"
              (click)="sortUsers('email')"
              style="cursor: pointer"
            >
              Email
              <i class="bi" [ngClass]="getSortIcon('email')"></i>
            </th>
            <th>Role</th>
            <th>Created by</th>
            <th
              class="sortable-header"
              (click)="sortUsers('createdOn')"
              style="cursor: pointer"
            >
              Created on
              <i class="bi" [ngClass]="getSortIcon('createdOn')"></i>
            </th>
            <th>Status</th>
            <th *ngIf="isGlobalAdmin" class="text-center" style="width: 120px">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading" class="text-center">
            <td colspan="7">
              <p-progress-spinner
                strokeWidth="8"
                fill="transparent"
                animationDuration=".5s"
                [style]="{ width: '25px', height: '35px', color: '#007bff' }"
              />
            </td>
          </tr>
          <tr *ngIf="!isLoading && error" class="text-center">
            <td colspan="7">
              <div class="alert alert-danger mb-0">
                {{ error }}
                <button class="btn btn-link text-danger" (click)="loadUsers()">
                  <i class="bi bi-arrow-clockwise"></i> Retry
                </button>
              </div>
            </td>
          </tr>
          <tr
            *ngIf="!isLoading && !error && users.length === 0"
            class="text-center"
          >
            <td colspan="7">No users found</td>
          </tr>
          <tr *ngFor="let user of users">
            <td class="my-2">{{ user.fullName }}</td>
            <td>{{ user.email }}</td>
            <td>{{ user.roles }}</td>
            <td>{{ user.createdByName }}</td>
            <td>{{ user.createdOn | date: "mediumDate" }}</td>
            <td>
              <span
                [ngClass]="user.isActive ? 'text-success' : 'text-danger'"
                >{{ user.isActive ? "Active" : "Inactive" }}</span
              >
            </td>
            <td *ngIf="isGlobalAdmin" class="action-buttons">
              <div
                class="d-flex align-items-center justify-content-center gap-1"
              >
                <button
                  class="btn btn-link text-secondary p-0"
                  (click)="viewUserDetails(user.id)"
                  title="View Details"
                >
                  <i class="bi bi-eye"></i>
                </button>
                <button
                  class="btn btn-link text-secondary p-0"
                  (click)="editUser(user.id)"
                  title="Edit User"
                >
                  <i class="bi bi-pencil"></i>
                </button>
                <button
                  class="btn btn-link p-0"
                  [ngClass]="user.isActive ? 'text-danger' : 'text-success'"
                  (click)="toggleUserStatus(user)"
                  [title]="user.isActive ? 'Deactivate User' : 'Activate User'"
                >
                  <i
                    class="bi"
                    [ngClass]="
                      user.isActive ? 'bi-person-x' : 'bi-person-check'
                    "
                  ></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="d-flex justify-content-between align-items-center mt-3 mb-4">
    <!-- Entries info text -->
    <div class="text-muted">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to
      {{ Math.min(currentPage * pageSize, totalItems) }} of
      {{ totalItems }} entries
    </div>

    <!-- Pagination -->
    <nav aria-label="Page navigation" *ngIf="totalPages > 1">
      <ul class="pagination mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage - 1)"
            tabindex="-1"
          >
            Previous
          </a>
        </li>

        <li
          class="page-item"
          *ngFor="let page of pages"
          [class.active]="page === currentPage"
        >
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(page)"
          >
            {{ page }}
          </a>
        </li>

        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage + 1)"
          >
            Next
          </a>
        </li>
      </ul>
    </nav>
  </div>
</div>

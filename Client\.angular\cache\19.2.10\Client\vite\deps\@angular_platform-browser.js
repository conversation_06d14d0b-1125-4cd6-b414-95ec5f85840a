import {
  By,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  Title,
  VERSION,
  disableDebugTools,
  enableDebugTools,
  provideClientHydration,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-HNR3MVT6.js";
import "./chunk-XBSQWBTS.js";
import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  DomEventsPlugin,
  DomRendererFactory2,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  KeyEventsPlugin,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  bootstrapApplication,
  createApplication,
  platformBrowser,
  provideProtrac<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from "./chunk-4KZ2WLHK.js";
import {
  getDOM
} from "./chunk-6GGXITPF.js";
import "./chunk-5MRPC2E5.js";
import "./chunk-UXETAPFX.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-WDMUDEB6.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};

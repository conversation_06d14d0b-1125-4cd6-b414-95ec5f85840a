﻿﻿namespace Server.Core.Pagination.PaginationParameterForEventModel {
    public class PaginationParameterEvent {

        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        public int PageNumber { get; set; } = 1;

        public int PageSize {
            get => _pageSize;
            set => _pageSize = value > MaxPageSize ? MaxPageSize : value;
        }

        public string? SortField { get; set; }
        public string? SortOrder { get; set; }

        // Filter properties based on the image reference
        public string? SearchTerm { get; set; }
        public DateTime? EventStartDate { get; set; }
        public string? EventStatus { get; set; }
        public string? Organizer { get; set; }
        public string? ApprovalStatus { get; set; }
        public string? Type { get; set; }
        public string? Category { get; set; }
        public DateTime? SubmittedOn { get; set; }
        public DateTime? EventReviewedOn { get; set; }
    }
}

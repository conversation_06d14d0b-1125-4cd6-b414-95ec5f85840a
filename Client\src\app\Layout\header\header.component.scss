.logout-btn {
  cursor: pointer !important;
}

.bell-button {
  position: relative;
  font-size: 1.2rem;
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 600px;
  max-height: 600px;
  overflow-y: auto;
  z-index: 1000;
  border-radius: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  h5 {
    font-size: 18px;
    font-weight: 600;
  }
}

.notification-list {
  padding: 0;
}

.notification-group {
  margin-bottom: 0;
}

.notification-group-header {
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;

  h6 {
    color: #666;
    font-weight: 600;
  }
}

.notification-item {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  align-items: flex-start;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-dot {
  width: 8px;
  height: 8px;
  background-color: #ff0000;
  border-radius: 50%;
  margin-right: 15px;
  margin-top: 6px;
  flex-shrink: 0;
}

.notification-content {
  flex-grow: 1;
  padding-right: 120px;
  font-size: 14px;
  line-height: 1.5;
}

.event-link,
.organizer-link {
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;
  &:hover {
    text-decoration: underline;
  }
}

.notification-time {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

.notification-status {
  position: absolute;
  right: 20px;
  top: 15px;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.pending-review {
    background-color: #fff4d4;
    color: #856404;
  }

  &.approved {
    background-color: #d4edda;
    color: #155724;
  }

  &.event-started {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  &.rejected {
    background-color: #f8d7da;
    color: #721c24;
  }
}

// Badge styles for notification count
.badge {
  font-size: 0.65rem;
  padding: 0.25em 0.5em;
}

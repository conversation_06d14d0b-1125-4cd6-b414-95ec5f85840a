import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HeaderComponent } from './Layout/header/header.component';
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
// Dialog functionality commented out as per request
// import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { AuthInterceptor } from './Core/Interceptors/auth.interceptor';
import { LoggingInterceptor } from './Core/Interceptors/logging.interceptor';
import {
  BrowserAnimationsModule,
  provideAnimations,
} from '@angular/platform-browser/animations';
import { CommonModule, DATE_PIPE_DEFAULT_TIMEZONE } from '@angular/common';
import { providePrimeNG } from 'primeng/config';
import Material from '@primeng/themes/material';
import { SharedModule } from './Core/shared.module';
// Session expiry dialog commented out as per request
// import { SessionExpiryDialogComponent } from './shared/components/session-expiry-dialog/session-expiry-dialog.component';
@NgModule({
  declarations: [AppComponent, HeaderComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    ToastModule,
    // DynamicDialogModule,
    BrowserAnimationsModule,
    CommonModule,
    SharedModule,
    // SessionExpiryDialogComponent,
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimations(),
    providePrimeNG({
      theme: {
        preset: Material,
        options: {
          prefix: 'p',
          darkModeSelector: '.dark-theme',
        },
      },
      ripple: true,
    }),
    MessageService,
    // DialogService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LoggingInterceptor,
      multi: true,
    },
    {
      provide: DATE_PIPE_DEFAULT_TIMEZONE,
      useValue: 'IST',
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
function provideAnimationsAsync():
  | import('@angular/core').Provider
  | import('@angular/core').EnvironmentProviders {
  throw new Error('Function not implemented.');
}

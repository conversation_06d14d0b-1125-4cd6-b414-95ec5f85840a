import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ResourceListComponent } from './resource-list/resource-list.component';
import { ResourceDetailsComponent } from './resource-details/resource-details.component';
import { AddEditListComponent } from './add-edit-list/add-edit-list.component';

const routes: Routes = [
  {
    path: '',
    component: ResourceListComponent,
  },
  {
    path: 'resource-details/:id',
    component: ResourceDetailsComponent,
  },
  {
    path: 'add-resource',
    component: AddEditListComponent,
  },
  {
    path: 'edit-resource/:id',
    component: AddEditListComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ResourceRoutingModule {}

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../Core/Services/auth.service';
import { Router, NavigationEnd } from '@angular/router';
import { UserDetailsService } from '../../Core/Services/UserDetails.service';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { Subscription, filter } from 'rxjs';
import { UserAvatarService } from '../../Core/Services/user-avatar.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
  standalone: true,
  imports: [CommonModule, ToastModule, MessagesModule, MessageModule],
  providers: [MessageService],
})
export class ProfileComponent implements OnInit, OnDestroy {
  userAvatar: string = '';
  userId: string = '';
  userName: string = '';
  userEmail: string = '';
  userRole: string = '';
  isActive: boolean = true;
  website: string = '';
  phoneNumber: string = '';
  description: string = '';
  facebookId: string = '';
  twitterId: string = '';
  isLoading: boolean = false;
  errorMessages: any[] = [];

  private routerSubscription: Subscription | null = null;
  private avatarSubscription: Subscription | null = null;

  constructor(
    private authService: AuthService,
    private userService: UserDetailsService,
    private router: Router,
    private messageService: MessageService,
    private userAvatarService: UserAvatarService,
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();

    this.avatarSubscription = this.userAvatarService
      .getUserAvatar()
      .subscribe((avatar) => {
        this.userAvatar = avatar;
      });

    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        if (this.router.url === '/profile') {
          this.loadUserProfile();
        }
      });
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    if (this.avatarSubscription) {
      this.avatarSubscription.unsubscribe();
    }
  }

  loadUserProfile(): void {
    this.authService.debugToken();

    const refreshed = this.authService.refreshUserInfoFromToken();

    const userInfo = this.authService.getUserInfo();

    const token = this.authService.getToken();

    if (userInfo) {
      this.userId = userInfo.id || userInfo.nameid || userInfo.sub || '';

      if (!this.userId && userInfo.name) {
        this.userName = userInfo.name;
        this.userEmail = userInfo.email || '';
      }

      if (this.userId) {
        this.isLoading = true;
        this.userService.getUserById(this.userId).subscribe({
          next: (user) => {
            this.userName = user.fullName || '';
            this.userEmail = user.email || '';
            this.userRole =
              user.roles && user.roles.length > 0 ? user.roles[0] : '';
            this.isActive = user.isActive;
            this.phoneNumber = user.phoneNumber || '';
            this.website = user.website || '';
            this.description = user.description || '';
            this.facebookId = user.facebook || '';
            this.twitterId = user.twitter || '';
            this.isLoading = false;

            this.errorMessages = [];
          },
          error: (error) => {
            console.error('Error loading user profile:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to load profile data',
            });

            this.errorMessages = [
              {
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load profile data from server',
              },
            ];

            this.isLoading = false;

            if (userInfo) {
              this.userName = userInfo.name || '';
              this.userEmail = userInfo.email || '';
              this.userRole = userInfo.role;
              this.isActive = userInfo.isActive;
              this.phoneNumber = userInfo.phoneNumber || '';
              this.website = userInfo.website || '';
              this.description = userInfo.description || '';
              this.facebookId = userInfo.facebook || '';
              this.twitterId = userInfo.twitter || '';
            }
          },
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'User information not found',
        });

        this.errorMessages = [
          {
            severity: 'error',
            summary: 'Error',
            detail: 'User information not found. Please try logging in again.',
          },
        ];
      }
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'User information not found',
      });

      this.errorMessages = [
        {
          severity: 'error',
          summary: 'Error',
          detail: 'User information not found. Please try logging in again.',
        },
      ];
    }
  }

  editProfile(): void {
    if (this.userId) {
      this.router.navigate(['/user-management/edit-user', this.userId]);
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'User ID not found',
      });

      this.errorMessages = [
        {
          severity: 'error',
          summary: 'Error',
          detail: 'Cannot edit profile: User ID not found',
        },
      ];
    }
  }

  // changePassword(): void {
  //   this.router.navigate(['/change-password']);
  // }

  refreshProfile(): void {
    this.loadUserProfile();
  }
}

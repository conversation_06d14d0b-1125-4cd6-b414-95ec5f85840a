<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1>LOGIN</h1>
      <p>Please enter your Email and password!</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="form-field">
        <span class="p-input-icon-left w-100">
          <i class="pi pi-user"></i>
          <input
            type="email"
            pInputText
            formControlName="email"
            placeholder="Email"
            class="w-100"
            [ngClass]="{
              'ng-invalid ng-dirty':
                loginForm.get('email')?.invalid &&
                loginForm.get('email')?.touched,
            }"
          />
        </span>
        <small
          *ngIf="
            loginForm.get('email')?.invalid && loginForm.get('email')?.touched
          "
          class="p-error block"
        >
          <span *ngIf="loginForm.get('email')?.errors?.['required']"
            >Email is required</span
          >
          <span *ngIf="loginForm.get('email')?.errors?.['email']"
            >Please enter a valid email address</span
          >
        </small>
      </div>

      <div class="form-field">
        <span class="p-input-icon-left w-100">
          <i class="pi pi-lock"></i>
          <input
            type="password"
            pInputText
            formControlName="password"
            placeholder="Password"
            class="w-100"
            [ngClass]="{
              'ng-invalid ng-dirty':
                loginForm.get('password')?.invalid &&
                loginForm.get('password')?.touched,
            }"
          />
        </span>
        <small
          *ngIf="
            loginForm.get('password')?.invalid &&
            loginForm.get('password')?.touched
          "
          class="p-error block"
        >
          <span *ngIf="loginForm.get('password')?.errors?.['required']"
            >Password is required</span
          >
        </small>
      </div>

      <div class="form-actions">
        <p-button
          type="submit"
          [disabled]="isLoading || loginForm.invalid"
          styleClass="login-button"
        >
          <ng-container *ngIf="isLoading; else loginText">
            <p-progressSpinner
              [style]="{ width: '24px', height: '24px' }"
              strokeWidth="4"
              animationDuration=".5s"
            ></p-progressSpinner>
          </ng-container>
          <ng-template #loginText>
            <span>Login</span>
          </ng-template>
        </p-button>
      </div>
    </form>
  </div>
</div>

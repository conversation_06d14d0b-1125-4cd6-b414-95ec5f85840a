export interface EventPaginationParams {
  pageNumber: number;
  pageSize: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: {
    searchTerm?: string;
    eventStartDate?: Date;
    eventStatus?: string;
    organizer?: string;
    approvalStatus?: string;
    type?: string;
    category?: string;
    submittedOn?: Date;
    eventReviewedOn?: Date;
  };
}

export interface PaginatedResponse<T> {
  items: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f7fbfd;
  border-radius: 8px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }

  .actions {
    display: flex;
    gap: 10px;
  }

  .btn-cancel,
  .btn-save {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;

    i {
      font-size: 16px;
    }
  }

  .btn-cancel {
    background-color: #f8f9fa;
    color: #dc3545;
    border: 1px solid #dc3545;

    &:hover {
      background-color: #f1f1f1;
    }
  }

  .btn-save {
    background-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
    }

    &:disabled {
      background-color: #e9a8ae;
      cursor: not-allowed;
    }
  }
}

.content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    label {
      font-weight: 500;
      color: #333;
    }

    .password-input-container {
      position: relative;
      display: flex;
      align-items: center;

      input {
        width: 100%;
        padding: 10px 40px 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 16px;
        transition: border-color 0.15s ease-in-out;

        &:focus {
          outline: none;
          border-color: #dc3545;
          box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        &.is-invalid {
          border-color: #dc3545;
        }
      }

      .toggle-password {
        position: absolute;
        right: 10px;
        background: none;
        border: none;
        cursor: pointer;
        color: #6c757d;

        &:hover {
          color: #343a40;
        }
      }
    }

    .invalid-feedback {
      color: #dc3545;
      font-size: 14px;
      margin-top: 4px;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

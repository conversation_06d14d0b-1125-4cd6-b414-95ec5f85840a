import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserDetailsService } from '../../../Core/Services/UserDetails.service';
import { HttpErrorResponse } from '@angular/common/http';
import { AuthService } from '../../../Core/Services/auth.service';
import { MessageService } from 'primeng/api';
import { UserFormData } from '../../../Core/Models/user-form.interface';
import { mapUserFormToPayload } from '../../../Core/utils/form-mappers';

@Component({
  selector: 'app-add-user',
  standalone: false,
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss',
  providers: [MessageService],
})
export class AddUserComponent implements OnInit {
  userForm!: FormGroup;
  isLoading: boolean = false;
  successMessage: string | null = null;
  validationErrors: string[] = [];
  isEditMode: boolean = false;
  userId: string | null = null;
  private originalUserData: any = null;

  private readonly defaultPasswords = {
    'Global Admin': 'Gadmin123@',
    'Department Admin': 'Dadmin123@',
    'Event Organizer': 'Eorganizor123@',
  } as const;

  constructor(
    private readonly router: Router,
    private readonly userService: UserDetailsService,
    private readonly fb: FormBuilder,
    private readonly authService: AuthService,
    private readonly messageService: MessageService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.initializeForm();

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.userId = params['id'];
        this.loadUserData(params['id']);
      }
    });
  }

  private initializeForm(): void {
    this.userForm = this.fb.group({
      FullName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      role: ['', [Validators.required]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      website: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+\\/?.*',
          ),
        ],
      ],
      description: [''],
      facebook: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?facebook\\.com\\/[a-zA-Z0-9\\.]+',
          ),
        ],
      ],
      twitter: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?(twitter\\.com|x\\.com)\\/[A-Za-z0-9_]{1,15}',
          ),
        ],
      ],
    });

    if (this.isEditMode) {
      this.userForm.get('email')?.disable();
    }
  }

  private loadUserData(userId: string): void {
    this.isLoading = true;
    this.userService.getUserById(userId).subscribe({
      next: (user) => {
        this.originalUserData = user;
        this.userForm.patchValue({
          FullName: user.fullName,
          email: user.email,
          role: user.roles[0],
          phoneNumber: user.phoneNumber,
          website: user.website,
          description: user.description,
          facebook: user.facebook,
          twitter: user.twitter,
        });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load user data',
        });
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    // Clear previous validation errors
    this.validationErrors = [];

    // Mark all fields as touched to show validation errors
    this.userForm.markAllAsTouched();

    if (this.userForm.invalid) {
      // Collect all validation errors
      this.collectValidationErrors();

      // Still show toast notification
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill out all required fields correctly',
      });
      return;
    }

    this.isLoading = true;
    const formData: UserFormData = {
      ...this.userForm.value,
      email: this.userForm.get('email')?.value, // Get email value even when disabled
    };

    const userInfo = this.authService.getUserInfo();
    const password =
      this.defaultPasswords[
        formData.role as keyof typeof this.defaultPasswords
      ];
    const creatorId = this.isEditMode
      ? this.originalUserData.createdBy
      : userInfo?.id;
    const userPayload = mapUserFormToPayload(formData, password, creatorId);

    this.saveUser(formData, userPayload);
  }

  private saveUser(formData: UserFormData, userPayload: any): void {
    const saveOperation =
      this.isEditMode && this.userId
        ? this.userService.updateUser(this.userId, userPayload)
        : this.userService.addUser(userPayload);

    const actionType = this.isEditMode ? 'Updated' : 'Added';

    saveOperation.subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.handleSuccess(formData, actionType);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail:
              response.message ||
              `Failed to ${this.isEditMode ? 'update' : 'add'} user.`,
          });
        }
        this.isLoading = false;
      },
      error: (err: HttpErrorResponse) => {
        this.handleError(
          err,
          this.isEditMode
            ? 'Failed to update user'
            : 'A user with this email already exists',
        );
      },
    });
  }

  private handleSuccess(formData: UserFormData, actionType: string): void {
    this.messageService.add({
      severity: 'success',
      summary: `User ${actionType} Successfully`,
      detail: this.isEditMode
        ? `${formData.FullName}'s information has been updated.`
        : `${formData.FullName} has been added as a ${formData.role}.`,
      life: 5000,
      closable: true,
      styleClass: 'success-toast',
    });

    this.successMessage = `User ${formData.FullName} has been ${actionType.toLowerCase()} successfully.`;

    setTimeout(() => {
      this.router.navigate(['/user-management']);
    }, 1500);
  }

  private handleError(err: HttpErrorResponse, defaultMessage: string): void {
    console.error('Error Response:', err);
    let errorDetail = defaultMessage;

    if (err.error?.errors && typeof err.error.errors === 'object') {
      const validationErrors = Object.entries(err.error.errors)
        .map(
          ([key, messages]) => `${key}: ${(messages as string[]).join(', ')}`,
        )
        .join('; ');
      errorDetail = `Validation errors: ${validationErrors}`;
    } else if (err.error?.message) {
      errorDetail = err.error.message;
    }

    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: errorDetail,
      life: 5000,
    });
    this.isLoading = false;
  }

  navigateBack(): void {
    this.router.navigate(['/user-management']);
  }

  private collectValidationErrors(): void {
    const controls = this.userForm.controls;

    if (controls['FullName'].errors) {
      this.validationErrors.push('Name is required');
    }

    if (controls['email'].errors) {
      if (controls['email'].errors['required']) {
        this.validationErrors.push('Email is required');
      } else if (controls['email'].errors['email']) {
        this.validationErrors.push('Please enter a valid email address');
      }
    }

    if (controls['phoneNumber'].errors) {
      if (controls['phoneNumber'].errors['required']) {
        this.validationErrors.push('Phone Number is required');
      } else if (controls['phoneNumber'].errors['pattern']) {
        this.validationErrors.push('Phone Number must be 10 digits');
      }
    }

    if (controls['role'].errors) {
      this.validationErrors.push('Role is required');
    }

    if (controls['website'].errors && controls['website'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Website URL');
    }

    if (controls['facebook'].errors && controls['facebook'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Facebook URL');
    }

    if (controls['twitter'].errors && controls['twitter'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Twitter URL');
    }
  }
}

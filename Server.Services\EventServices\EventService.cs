﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Core.Entities.Events.EventModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Entities.UserManagement.UserModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParameterForEventModel;
using Server.EntityFramworkCore.Data;
using Server.Services.S3Services;

namespace Server.Services.EventServices {
    public class EventService : IEventService {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly IHostEnvironment _environment;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IS3Service _s3Service;

        public EventService(
            ApplicationDbContext context,
            IMapper mapper,
            IHostEnvironment environment,
            UserManager<ApplicationUser> userManager,
            IS3Service s3Service) {
            _context = context;
            _mapper = mapper;
            _environment = environment;
            _userManager = userManager;
            _s3Service = s3Service;
        }

        public async Task<ApiResponse<EventResponseDto>> CreateEvent(EventCreateDto dto) {
            try {
                // Create the event
                var newEvent = _mapper.Map<Event>(dto);
                newEvent.CreatedAt = DateTime.UtcNow;

                // Ensure SubmitterName is set
                if (string.IsNullOrEmpty(newEvent.SubmitterName)) {
                    // Try to get the organizer's name if available
                    if (!string.IsNullOrEmpty(newEvent.OrganizerId)) {
                        var organizer = await _userManager.FindByIdAsync(newEvent.OrganizerId);
                        if (organizer != null) {
                            newEvent.SubmitterName = organizer.FullName;
                        }
                    }
                }


                // Add the event to the context
                await _context.Events.AddAsync(newEvent);
                await _context.SaveChangesAsync();

                // Create and associate the location
                if (dto.Location != null) {
                    var location = _mapper.Map<EventLocation>(dto.Location);
                    location.EventId = newEvent.Id;
                    await _context.EventLocations.AddAsync(location);
                }

                // Create and associate the contact details
                if (dto.ContactDetails != null) {
                    var contactDetails = _mapper.Map<EventContactDetails>(dto.ContactDetails);
                    contactDetails.EventId = newEvent.Id;
                    await _context.EventContactDetails.AddAsync(contactDetails);
                }

                await _context.SaveChangesAsync();

                // Get the complete event with navigation properties
                var createdEvent = await GetCompleteEventById(newEvent.Id);
                var responseDto = _mapper.Map<EventResponseDto>(createdEvent);

                // Set the image URL
                if (!string.IsNullOrEmpty(createdEvent.EventImagePath)) {

                    responseDto.EventImageUrl = _s3Service.GetFileUrl(createdEvent.EventImagePath);
                }

                return ApiResponse<EventResponseDto>.Success(responseDto, "Event created successfully");
            }
            catch (Exception ex) {
                return ApiResponse<EventResponseDto>.Failure($"Error creating event: {ex.Message}");
            }
        }

        public async Task<ApiResponse<EventResponseDto>> UpdateEvent(EventUpdateDto dto) {
            try {
                var existingEvent = await _context.Events
                    .Include(e => e.Location)
                    .Include(e => e.ContactDetails)
                    .FirstOrDefaultAsync(e => e.Id == dto.Id);

                if (existingEvent == null) {
                    return ApiResponse<EventResponseDto>.Failure($"Event with ID {dto.Id} not found");
                }

                // Handle image update
                if (dto.EventImage != null) {
                    // Delete old image if exists
                    if (!string.IsNullOrEmpty(existingEvent.EventImagePath)) {
                        DeleteFile(existingEvent.EventImagePath);
                    }

                    // Upload new image
                    existingEvent.EventImagePath = await UploadFile(dto.EventImage, "EventImages");
                }

                // Update the event properties
                _mapper.Map(dto, existingEvent);
                existingEvent.UpdatedAt = DateTime.UtcNow;

                // Update location
                if (existingEvent.Location != null && dto.Location != null) {
                    _mapper.Map(dto.Location, existingEvent.Location);
                }
                else if (dto.Location != null) {
                    var location = _mapper.Map<EventLocation>(dto.Location);
                    location.EventId = existingEvent.Id;
                    await _context.EventLocations.AddAsync(location);
                }

                // Update contact details
                if (existingEvent.ContactDetails != null && dto.ContactDetails != null) {
                    _mapper.Map(dto.ContactDetails, existingEvent.ContactDetails);
                }
                else if (dto.ContactDetails != null) {
                    var contactDetails = _mapper.Map<EventContactDetails>(dto.ContactDetails);
                    contactDetails.EventId = existingEvent.Id;
                    await _context.EventContactDetails.AddAsync(contactDetails);
                }

                await _context.SaveChangesAsync();

                // Get the updated event with navigation properties
                var updatedEvent = await GetCompleteEventById(existingEvent.Id);
                var responseDto = _mapper.Map<EventResponseDto>(updatedEvent);

                // Set the image URL
                if (!string.IsNullOrEmpty(updatedEvent.EventImagePath)) {
                    responseDto.EventImageUrl = _s3Service.GetFileUrl(updatedEvent.EventImagePath);
                }

                return ApiResponse<EventResponseDto>.Success(responseDto, "Event updated successfully");
            }
            catch (Exception ex) {
                return ApiResponse<EventResponseDto>.Failure($"Error updating event: {ex.Message}");
            }
        }

        public async Task<ApiResponse<EventResponseDto>> GetEventById(int id) {
            try {
                var eventEntity = await GetCompleteEventById(id);

                if (eventEntity == null) {
                    return ApiResponse<EventResponseDto>.Failure($"Event with ID {id} not found");
                }

                var responseDto = _mapper.Map<EventResponseDto>(eventEntity);

                // Set the image URL
                if (!string.IsNullOrEmpty(eventEntity.EventImagePath)) {

                    responseDto.EventImageUrl = _s3Service.GetFileUrl(eventEntity.EventImagePath);

                }

                return ApiResponse<EventResponseDto>.Success(responseDto);
            }
            catch (Exception ex) {
                return ApiResponse<EventResponseDto>.Failure($"Error retrieving event: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> DeleteEvent(int id) {
            try {
                var eventEntity = await _context.Events.FindAsync(id);

                if (eventEntity == null) {
                    return new ApiResponse<bool> {
                        IsSuccess = false,
                        Message = $"Event with ID {id} not found",
                        Data = false
                    };
                }

                // Delete the event image if it exists
                if (!string.IsNullOrEmpty(eventEntity.EventImagePath)) {
                    DeleteFile(eventEntity.EventImagePath);
                }

                _context.Events.Remove(eventEntity);
                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Event deleted successfully");
            }
            catch (Exception ex) {
                return ApiResponse<bool>.Failure($"Error deleting event: {ex.Message}", false);
            }
        }

        public async Task<PagedResponse<EventResponseDto>> GetAllEvents(PaginationParameterEvent parameters) {
            try {
                // Start with the base query
                var query = _context.Events
                    .Include(e => e.Location)
                    .Include(e => e.ContactDetails)
                    .Include(e => e.Organizer)
                    .Include(e => e.ReviewedBy)
                    .AsQueryable();

                // Apply filters
                // Search term (title)
                if (!string.IsNullOrWhiteSpace(parameters.SearchTerm)) {
                    query = query.Where(e => e.Title.Contains(parameters.SearchTerm));
                }

                // Event start date
                if (parameters.EventStartDate.HasValue) {
                    var startDate = parameters.EventStartDate.Value.Date;
                    var endDate = startDate.AddDays(1).AddTicks(-1);
                    query = query.Where(e => e.EventStarts >= startDate && e.EventStarts <= endDate);
                }

                // Event status
                if (!string.IsNullOrWhiteSpace(parameters.EventStatus) &&
                    Enum.TryParse<EventStatus>(parameters.EventStatus, out var eventStatus)) {
                    query = query.Where(e => e.Status == eventStatus);
                }

                // Organizer - search by organizer ID, organizer name, or submitter name
                if (!string.IsNullOrWhiteSpace(parameters.Organizer)) {
                    query = query.Where(e =>
                        e.OrganizerId == parameters.Organizer ||
                        (e.Organizer != null && e.Organizer.FullName.Contains(parameters.Organizer)) ||
                        (e.SubmitterName != null && e.SubmitterName.Contains(parameters.Organizer))
                    );
                }

                // Approval status
                if (!string.IsNullOrWhiteSpace(parameters.ApprovalStatus)) {
                    bool isApproved = parameters.ApprovalStatus.ToLower() == "true";
                    query = query.Where(e => e.IsApproved == isApproved);
                }

                // Event type
                if (!string.IsNullOrWhiteSpace(parameters.Type)) {

                    // Try to parse as enum name first
                    if (Enum.TryParse<EventType>(parameters.Type, out var eventTypeByName)) {

                        query = query.Where(e => e.Type == eventTypeByName);
                    }
                    //// If that fails, try to parse as integer
                    //else if (int.TryParse(parameters.Type, out var eventTypeValue) &&
                    //         Enum.IsDefined(typeof(EventType), eventTypeValue)) {
                    //    var eventTypeByValue = (EventType)eventTypeValue;
                    //    Console.WriteLine($"EventService.GetAllEvents: Parsed Type as integer: {eventTypeValue} => {eventTypeByValue}");
                    //    query = query.Where(e => e.Type == eventTypeByValue);
                    //}
                    //else {
                    //    Console.WriteLine($"EventService.GetAllEvents: Could not parse Type: {parameters.Type}");
                    //}
                }

                // Category
                if (!string.IsNullOrWhiteSpace(parameters.Category)) {


                    // Try to parse as enum name first
                    if (Enum.TryParse<Category>(parameters.Category, out var categoryByName)) {
                        query = query.Where(e => e.Category == categoryByName);
                    }
                    //// If that fails, try to parse as integer
                    //else if (int.TryParse(parameters.Category, out var categoryValue) &&
                    //         Enum.IsDefined(typeof(Category), categoryValue)) {
                    //    var categoryByValue = (Category)categoryValue;
                    //    Console.WriteLine($"EventService.GetAllEvents: Parsed Category as integer: {categoryValue} => {categoryByValue}");
                    //    query = query.Where(e => e.Category == categoryByValue);
                    //}
                    //else {
                    //    Console.WriteLine($"EventService.GetAllEvents: Could not parse Category: {parameters.Category}");
                    //}
                }

                // Submitted on date
                if (parameters.SubmittedOn.HasValue) {
                    var submittedDate = parameters.SubmittedOn.Value.Date;
                    var endDate = submittedDate.AddDays(1).AddTicks(-1);
                    query = query.Where(e => e.SubmittedOn >= submittedDate && e.SubmittedOn <= endDate);
                }

                // Event reviewed on date
                if (parameters.EventReviewedOn.HasValue) {
                    var reviewedDate = parameters.EventReviewedOn.Value.Date;
                    var endDate = reviewedDate.AddDays(1).AddTicks(-1);
                    query = query.Where(e => e.ReviewedOn >= reviewedDate && e.ReviewedOn <= endDate);
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortField)) {
                    var isAscending = parameters.SortOrder?.ToLower() != "desc";
                    query = ApplySorting(query, parameters.SortField, isAscending);
                }
                else {
                    // Default sorting by CreatedAt descending
                    query = query.OrderByDescending(e => e.CreatedAt);
                }

                // Get total count before pagination
                var totalItems = await query.CountAsync();

                // Apply pagination
                var items = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();

                // Map to DTOs
                var eventDtos = _mapper.Map<List<EventResponseDto>>(items);

                // Set image URLs
                foreach (var dto in eventDtos) {
                    var eventEntity = items.FirstOrDefault(e => e.Id == dto.Id);
                    if (eventEntity != null && !string.IsNullOrEmpty(eventEntity.EventImagePath)) {
                        dto.EventImageUrl = _s3Service.GetFileUrl(eventEntity.EventImagePath);
                    }
                }

                return new PagedResponse<EventResponseDto>(eventDtos, totalItems, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex) {
                // Log the exception
                return new PagedResponse<EventResponseDto>(new List<EventResponseDto>(), 0, 1, parameters.PageSize);
            }
        }

        public async Task<ApiResponse<bool>> ApproveEvent(int id, string reviewerId) {
            try {
                var eventEntity = await _context.Events.FindAsync(id);

                if (eventEntity == null) {
                    return new ApiResponse<bool> {
                        IsSuccess = false,
                        Message = $"Event with ID {id} not found",
                        Data = false
                    };
                }

                eventEntity.Status = EventStatus.Approved;
                eventEntity.IsApproved = true;
                eventEntity.ReviewedOn = DateTime.UtcNow;
                eventEntity.ReviewedById = reviewerId;
                eventEntity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Event approved successfully");
            }
            catch (Exception ex) {
                return ApiResponse<bool>.Failure($"Error approving event: {ex.Message}", false);
            }
        }

        public async Task<ApiResponse<bool>> RejectEvent(int id, string reviewerId, string rejectionReason) {
            try {
                var eventEntity = await _context.Events.FindAsync(id);

                if (eventEntity == null) {
                    return ApiResponse<bool>.Failure($"Event with ID {id} not found", false);
                }

                eventEntity.Status = EventStatus.Rejected;
                eventEntity.IsApproved = false;
                eventEntity.ReviewedOn = DateTime.UtcNow;
                eventEntity.ReviewedById = reviewerId;
                eventEntity.RejectionReason = rejectionReason;
                eventEntity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Event rejected successfully");
            }
            catch (Exception ex) {
                return ApiResponse<bool>.Failure($"Error rejecting event: {ex.Message}", false);
            }
        }

        public async Task<ApiResponse<bool>> SubmitEvent(int id) {
            try {
                var eventEntity = await _context.Events.FindAsync(id);

                if (eventEntity == null) {
                    return ApiResponse<bool>.Failure($"Event with ID {id} not found", false);
                }

                eventEntity.Status = EventStatus.Submitted;
                eventEntity.SubmittedOn = DateTime.UtcNow;
                eventEntity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Event submitted successfully");
            }
            catch (Exception ex) {
                return ApiResponse<bool>.Failure($"Error submitting event: {ex.Message}", false);
            }
        }

        public async Task<ApiResponse<bool>> CancelEvent(int id) {
            try {
                var eventEntity = await _context.Events.FindAsync(id);

                if (eventEntity == null) {
                    return ApiResponse<bool>.Failure($"Event with ID {id} not found", false);
                }

                eventEntity.Status = EventStatus.Cancelled;
                eventEntity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Event cancelled successfully");
            }
            catch (Exception ex) {
                return ApiResponse<bool>.Failure($"Error cancelling event: {ex.Message}", false);
            }
        }

        public async Task<ApiResponse<bool>> CheckAndDeleteExpiredEvents() {
            try {
                var now = DateTime.UtcNow;

                var eventsToDelete = new List<Event>();

                // Find approved events that have ended (current time is past the event end time)
                var approvedEventsToDelete = await _context.Events
                    .Where(e => e.Status == EventStatus.Approved && e.EventEnds < now)
                    .ToListAsync();

                foreach (var evt in approvedEventsToDelete) {
                }

                // Find rejected events that have reached their start time
                var rejectedEventsToDelete = await _context.Events
                    .Where(e => e.Status == EventStatus.Rejected && e.EventStarts < now)
                    .ToListAsync();

                foreach (var evt in rejectedEventsToDelete) {
                }

                // Combine the lists
                eventsToDelete.AddRange(approvedEventsToDelete);
                eventsToDelete.AddRange(rejectedEventsToDelete);

                if (eventsToDelete.Any()) {
                    // Remove the events
                    _context.Events.RemoveRange(eventsToDelete);
                    await _context.SaveChangesAsync();

                    return ApiResponse<bool>.Success(
                        true,
                        $"Successfully deleted {eventsToDelete.Count} expired events " +
                        $"({approvedEventsToDelete.Count} approved events that ended, " +
                        $"{rejectedEventsToDelete.Count} rejected events that passed their start time)"
                    );
                }

                return ApiResponse<bool>.Success(true, "No expired events to delete");
            }
            catch (Exception ex) {

                return ApiResponse<bool>.Failure($"Error checking and deleting expired events: {ex.Message}", false);
            }
        }

        public async Task<string> UploadFile(IFormFile file, string folderName) {
            if (file == null || file.Length == 0)
                return null;

            try {
                // Use S3 service to upload the file
                string key = await _s3Service.UploadFileAsync(file, folderName);

                // Return the key (path) of the file in S3
                return key;
            }
            catch (Exception ex) {
                // Log the exception
                throw; // Rethrow to handle at the service level
            }
        }

        public void DeleteFile(string filePath) {
            if (string.IsNullOrEmpty(filePath))
                return;

            try {
                // Use S3 service to delete the file
                _s3Service.DeleteFileAsync(filePath).Wait();
            }
            catch (Exception ex) {
                // Log the exception
            }
        }


        private async Task<Event> GetCompleteEventById(int id) {
            return await _context.Events
                .Include(e => e.Location)
                .Include(e => e.ContactDetails)
                .Include(e => e.Organizer)
                .Include(e => e.ReviewedBy)
                .FirstOrDefaultAsync(e => e.Id == id);
        }



        private IQueryable<Event> ApplySorting(IQueryable<Event> query, string sortField, bool isAscending) {
            return sortField.ToLower() switch {
                "title" => isAscending ? query.OrderBy(e => e.Title) : query.OrderByDescending(e => e.Title),
                "type" => isAscending ? query.OrderBy(e => e.Type) : query.OrderByDescending(e => e.Type),
                "category" => isAscending ? query.OrderBy(e => e.Category) : query.OrderByDescending(e => e.Category),
                "eventstarts" => isAscending ? query.OrderBy(e => e.EventStarts) : query.OrderByDescending(e => e.EventStarts),
                "eventends" => isAscending ? query.OrderBy(e => e.EventEnds) : query.OrderByDescending(e => e.EventEnds),
                "status" => isAscending ? query.OrderBy(e => e.Status) : query.OrderByDescending(e => e.Status),
                "isapproved" => isAscending ? query.OrderBy(e => e.IsApproved) : query.OrderByDescending(e => e.IsApproved),
                "submittedon" => isAscending ? query.OrderBy(e => e.SubmittedOn) : query.OrderByDescending(e => e.SubmittedOn),
                "reviewedon" => isAscending ? query.OrderBy(e => e.ReviewedOn) : query.OrderByDescending(e => e.ReviewedOn),
                "createdat" => isAscending ? query.OrderBy(e => e.CreatedAt) : query.OrderByDescending(e => e.CreatedAt),
                "updatedat" => isAscending ? query.OrderBy(e => e.UpdatedAt) : query.OrderByDescending(e => e.UpdatedAt),
                _ => isAscending ? query.OrderBy(e => e.CreatedAt) : query.OrderByDescending(e => e.CreatedAt),
            };
        }


    }
}

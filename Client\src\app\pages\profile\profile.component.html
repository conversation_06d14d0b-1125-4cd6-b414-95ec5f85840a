<div class="profile-container">
  <div class="profile-header">
    <h2>Profile</h2>
    <div class="profile-actions">
      <!-- <button
        class="change-password-btn"
        (click)="changePassword()"
        [disabled]="isLoading"
      >
        <i class="bi bi-key"></i> Change Password
      </button> -->
      <button class="edit-btn" (click)="editProfile()" [disabled]="isLoading">
        <i class="pi pi-pencil"></i> Edit
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border text-danger" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Loading profile data...</p>
  </div>

  <!-- Error Messages -->
  <p-messages
    *ngIf="errorMessages.length > 0"
    [(value)]="errorMessages"
    [closable]="true"
    [style]="{ marginBottom: '1rem' }"
  ></p-messages>

  <!-- Profile Content -->
  <div class="profile-content" *ngIf="!isLoading">
    <div class="profile-avatar-container">
      <img [src]="userAvatar" alt="User Avatar" class="profile-avatar" />
    </div>

    <div class="profile-details">
      <div class="profile-row">
        <div class="profile-field">
          <label>Name</label>
          <div class="field-value">{{ userName }}</div>
        </div>
        <div class="profile-field">
          <label>Email</label>
          <div class="field-value">{{ userEmail }}</div>
        </div>
        <div class="profile-field">
          <label>Profile Status</label>
          <div class="status-indicator">
            <span class="status-dot" [class.active]="isActive"></span>
            <span>{{ isActive ? "Active" : "Inactive" }}</span>
          </div>
        </div>
      </div>

      <div class="profile-row">
        <div class="profile-field">
          <label>Contact Number</label>
          <div class="field-value">{{ phoneNumber || "Not provided" }}</div>
        </div>
        <div class="profile-field">
          <label>User Role</label>
          <div class="field-value">{{ userRole }}</div>
        </div>
        <div class="profile-field">
          <label>Website</label>
          <div class="field-value">{{ website || "Not provided" }}</div>
        </div>
      </div>

      <div class="profile-row full-width">
        <div class="profile-field">
          <label>Description</label>
          <div class="field-value description">
            {{ description || "No description available" }}
          </div>
        </div>
      </div>

      <h3 class="social-media-title">Social Media Links</h3>

      <div class="profile-row">
        <div class="profile-field">
          <label>Facebook</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 10px;
                z-index: 1;
              "
            >
              <i class="pi pi-facebook" style="color: #4267b2"></i>
            </div>
            <input
              type="text"
              [value]="facebookId || ''"
              class="form-control social-input"
              readonly
              style="padding-left: 35px; width: 100%"
            />
          </div>
        </div>
        <div class="profile-field">
          <label>Twitter (X)</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 10px;
                z-index: 1;
              "
            >
              <i class="pi pi-twitter" style="color: #1da1f2"></i>
            </div>
            <input
              type="text"
              [value]="twitterId || ''"
              class="form-control social-input"
              readonly
              style="padding-left: 35px; width: 100%"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Toast for notifications -->
<p-toast></p-toast>

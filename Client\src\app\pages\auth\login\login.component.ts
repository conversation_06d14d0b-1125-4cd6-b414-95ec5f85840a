import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../Core/Services/auth.service';
import { LoginRequest } from '../../../Core/Models/login-request';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  errorMessage: string | null = null;
  isLoading = false;

  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);

  constructor(private messageService: MessageService) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });
  }

  onSubmit(): void {
    if (this.loginForm.invalid) return;

    this.isLoading = true;
    const loginData: LoginRequest = this.loginForm.value;

    this.authService.login(loginData).subscribe({
      next: (res) => this.handleLoginResponse(res),
      error: (error) => this.handleLoginError(error),
    });
  }

  private handleLoginResponse(res: any): void {
    this.isLoading = false;

    if (!res.isSuccess) {
      this.showMessage('error', 'Login Failed', res.message || 'Login failed');
      this.errorMessage = res.message || 'Login failed';
      return;
    }

    if (res.message?.toLowerCase().includes('inactive')) {
      this.showMessage(
        'warn',
        'Account Inactive',
        'Your account is inactive. Please contact an administrator to activate your account.',
        6000,
      );
      this.errorMessage =
        'Your account is inactive. Please contact an administrator to activate your account.';
      return;
    }

    const requiresOtp = res.data?.RequiresOtp || res.data?.requiresOtp;
    if (requiresOtp) {
      this.showMessage(
        'info',
        'OTP Required',
        'Please check your email for the OTP',
      );
      const userId = res.data?.UserId || res.data?.userId;
      this.router.navigate(['/auth/VerifyOtp'], { state: { userId } });
    } else {
      this.showMessage('success', 'Login Success', res.message);
      this.router.navigate(['/dashboard']);
    }
  }

  private handleLoginError(error: any): void {
    this.isLoading = false;
    const errorMessage = error.error?.message || 'Invalid email or password.';
    this.showMessage('error', 'Login Failed', errorMessage);
    this.errorMessage = errorMessage;
  }

  private showMessage(
    severity: string,
    summary: string,
    detail: string,
    life = 4000,
  ): void {
    this.messageService.add({
      severity,
      summary,
      detail,
      styleClass: `custom-${severity}-toast`,
      life,
    });
  }
}

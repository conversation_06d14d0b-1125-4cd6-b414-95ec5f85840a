.resource-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.card {
  transition: transform 0.2s;
  border-radius: 8px;

  &:hover {
    transform: translateY(-2px);
  }
}

.form-label {
  font-weight: 500;
  color: #333;
  position: relative;

  .text-danger {
    display: inline-block;
    font-size: 1rem;
    margin-left: 2px;
    line-height: 1;
    position: relative;
    top: 2px;
  }
}

// Helper text styling
small.text-muted {
  font-size: 0.8rem;
  color: #6c757d;

  &.mb-1 {
    margin-bottom: 0.3rem !important;
  }
}

// Required field indicator
.required-indicator {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
  display: inline-block;
}

// Error messages
.text-danger:not(.form-label .text-danger) {
  color: #dc3545 !important;
  font-size: 0.85rem;
  display: block;
  margin-top: 5px;

  .bi-exclamation-circle {
    font-size: 0.9rem;
  }
}

.upload-container {
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e9ecef;
  }
}

.image-preview img {
  object-fit: cover;
  border-radius: 4px;
}

.form-control,
.form-select {
  border-color: #ced4da;

  &:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;
    background-image: none;
    padding-right: 0.75rem;
  }
}

.form-check-input:checked {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Custom styles for services inputs */
[formArrayName="services"] {
  .form-control {
    border-width: 0 0 1px 0;
    border-color: #dc3545;
    border-radius: 0;
    padding-left: 0;
    padding-right: 30px;

    &:focus {
      box-shadow: none;
      border-color: #dc3545;
    }
  }
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;

  &:hover {
    background-color: #c82333;
    border-color: #bd2130;
  }
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;

  &:hover {
    background-color: #dc3545;
    color: white;
  }
}

// Social media icons and inputs
.bi-facebook {
  color: #3b5998;
}

.bi-twitter {
  color: #1da1f2;
}

.bi-instagram {
  color: #e1306c;
}

.bi-linkedin {
  color: #0077b5;
}

// Social media input styling
.input-group {
  .form-control.is-invalid {
    z-index: 0;

    & + .invalid-feedback {
      display: block;
    }
  }

  &:has(.form-control.is-invalid) {
    .input-group-text {
      border-color: #dc3545;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .form-check-inline {
    display: block;
    margin-bottom: 10px;
  }
}

// Toast message styling
:host ::ng-deep {
  .p-toast {
    opacity: 1 !important;

    .p-toast-message {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 1rem;
      min-width: 300px;

      .p-toast-summary {
        font-weight: 600;
        font-size: 1.1rem;
      }

      .p-toast-detail {
        margin-top: 6px;
        font-size: 0.95rem;
      }

      &.p-toast-message-success {
        background-color: #e8f5e9;
        border-left: 6px solid #4caf50;
        color: #2e7d32;

        .p-toast-icon {
          color: #2e7d32;
          font-size: 1.5rem;
        }
      }

      &.p-toast-message-error {
        background-color: #ffebee;
        border-left: 6px solid #f44336;
        color: #c62828;

        .p-toast-icon {
          color: #c62828;
          font-size: 1.5rem;
        }
      }

      &.p-toast-message-info {
        background-color: #e3f2fd;
        border-left: 6px solid #2196f3;
        color: #0d47a1;

        .p-toast-icon {
          color: #0d47a1;
          font-size: 1.5rem;
        }
      }
    }
  }
}
